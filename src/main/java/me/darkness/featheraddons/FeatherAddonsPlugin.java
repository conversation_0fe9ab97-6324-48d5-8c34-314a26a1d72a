package me.darkness.featheraddons;

import me.darkness.featheraddons.commands.FeatherAddonsCommand;
import me.darkness.featheraddons.configuration.ConfigManager;
import me.darkness.featheraddons.listeners.FeatherPlayerListener;
import me.darkness.featheraddons.managers.DiscordManager;
import me.darkness.featheraddons.managers.ModManager;
import me.darkness.featheraddons.managers.ServerBackgroundManager;
import me.darkness.featheraddons.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;
import java.util.logging.Level;

public final class FeatherAddonsPlugin extends JavaPlugin {

    private static FeatherAddonsPlugin instance;
    
    private ConfigManager configManager;
    private ModManager modManager;
    private DiscordManager discordManager;
    private ServerBackgroundManager backgroundManager;
    private MessageUtils messageUtils;

    @Override
    public void onEnable() {
        instance = this;
        
        // Check if FeatherServerAPI is available
        if (!Bukkit.getPluginManager().isPluginEnabled("FeatherServerAPI")) {
            getLogger().severe("FeatherServerAPI plugin not found! This plugin requires FeatherServerAPI to work.");
            getLogger().severe("Download it from: https://github.com/FeatherMC/feather-server-api/releases");
            Bukkit.getPluginManager().disablePlugin(this);
            return;
        }
        
        getLogger().info("Starting 777-FeatherAddons...");
        
        // Create plugin directories
        createDirectories();
        
        // Initialize configuration
        initializeConfiguration();
        
        // Initialize managers
        initializeManagers();
        
        // Register commands and listeners
        registerCommandsAndListeners();

        // Server background is now loaded automatically in ServerBackgroundManager constructor

        getLogger().info("777-FeatherAddons has been enabled successfully!");
    }

    @Override
    public void onDisable() {
        getLogger().info("Disabling 777-FeatherAddons...");

        // Cleanup managers
        if (discordManager != null) {
            try {
                discordManager.shutdown();
            } catch (Exception e) {
                getLogger().warning("Error during Discord manager shutdown: " + e.getMessage());
            }
        }

        getLogger().info("777-FeatherAddons has been disabled!");
    }
    
    private void createDirectories() {
        // Create main plugin directory
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }
    }
    
    private void initializeConfiguration() {
        // Save default config if it doesn't exist
        saveDefaultConfig();
        
        // Initialize config manager
        configManager = new ConfigManager(this);
        configManager.loadConfiguration();
        
        // Initialize message utils
        messageUtils = new MessageUtils(this);
    }
    
    private void initializeManagers() {
        // Initialize mod manager
        try {
            modManager = new ModManager(this);
            getLogger().info("Mod manager initialized successfully");
        } catch (Exception e) {
            getLogger().severe("Failed to initialize mod manager: " + e.getMessage());
        }

        // Initialize discord manager
        try {
            discordManager = new DiscordManager(this);
            getLogger().info("Discord manager initialized successfully");
        } catch (Exception e) {
            getLogger().warning("Failed to initialize Discord manager: " + e.getMessage());
            getLogger().warning("Discord Rich Presence will be disabled");
        }

        // Initialize background manager
        try {
            backgroundManager = new ServerBackgroundManager(this);
            getLogger().info("Background manager initialized successfully");
        } catch (Exception e) {
            getLogger().warning("Failed to initialize background manager: " + e.getMessage());
            getLogger().warning("Server background functionality will be disabled");
        }
    }
    
    private void registerCommandsAndListeners() {
        // Register main command
        FeatherAddonsCommand mainCommand = new FeatherAddonsCommand(this);
        getCommand("777featheraddons").setExecutor(mainCommand);
        getCommand("777featheraddons").setTabCompleter(mainCommand);
        
        // Register event listeners
        Bukkit.getPluginManager().registerEvents(new FeatherPlayerListener(this), this);
    }
    
    private void loadServerBackground() {
        if (configManager.isServerBackgroundEnabled() && backgroundManager != null) {
            Bukkit.getScheduler().runTaskAsynchronously(this, () -> {
                try {
                    backgroundManager.loadBackground();
                } catch (Exception e) {
                    getLogger().log(Level.WARNING, "Failed to load server background", e);
                }
            });
        }
    }
    
    public void reloadConfiguration() {
        // Reload config
        reloadConfig();
        configManager.loadConfiguration();

        // Reload managers
        if (modManager != null) {
            modManager.reload();
        }
        if (discordManager != null) {
            discordManager.reload();
        }
        if (backgroundManager != null) {
            backgroundManager.reload();
        }

        getLogger().info("Configuration reloaded successfully!");
    }
    
    // Getters
    public static FeatherAddonsPlugin getInstance() {
        return instance;
    }
    
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    public ModManager getModManager() {
        return modManager;
    }
    
    public DiscordManager getDiscordManager() {
        return discordManager;
    }
    
    public ServerBackgroundManager getBackgroundManager() {
        return backgroundManager;
    }
    
    public MessageUtils getMessageUtils() {
        return messageUtils;
    }
}
