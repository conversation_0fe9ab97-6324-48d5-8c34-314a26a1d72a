package me.darkness.featheraddons.managers;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.model.FeatherMod;
import net.digitalingot.feather.serverapi.api.player.FeatherPlayer;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class ModManager {

    private final FeatherAddonsPlugin plugin;
    private List<String> defaultBlockedMods;

    public ModManager(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
        loadConfiguration();
    }

    public void loadConfiguration() {
        this.defaultBlockedMods = new ArrayList<>(plugin.getConfigManager().getBlockedMods());
    }

    public void reload() {
        loadConfiguration();
    }

    public void applyDefaultBlockedMods(FeatherPlayer featherPlayer) {
        if (!plugin.getConfigManager().isAutoBlockOnJoin() || defaultBlockedMods.isEmpty()) {
            return;
        }

        List<FeatherMod> modsToBlock = defaultBlockedMods.stream()
                .map(FeatherMod::new)
                .collect(Collectors.toList());

        featherPlayer.blockMods(modsToBlock);
        // Nie pokazujemy wiadomości przy wejściu na serwer - tylko ciche blokowanie
    }

    public boolean blockMod(String modName, UUID playerUuid) {
        FeatherPlayer featherPlayer = FeatherAPI.getPlayerService().getPlayer(playerUuid);
        if (featherPlayer == null) {
            return false;
        }

        FeatherMod mod = new FeatherMod(modName);
        featherPlayer.blockMods(List.of(mod));

        return true;
    }

    public boolean unblockMod(String modName, UUID playerUuid) {
        FeatherPlayer featherPlayer = FeatherAPI.getPlayerService().getPlayer(playerUuid);
        if (featherPlayer == null) {
            return false;
        }

        FeatherMod mod = new FeatherMod(modName);
        featherPlayer.unblockMods(List.of(mod));

        return true;
    }
    
    public boolean blockMods(List<String> modNames, UUID playerUuid) {
        FeatherPlayer featherPlayer = FeatherAPI.getPlayerService().getPlayer(playerUuid);
        if (featherPlayer == null) {
            return false;
        }

        List<FeatherMod> mods = modNames.stream()
                .map(FeatherMod::new)
                .collect(Collectors.toList());

        featherPlayer.blockMods(mods);

        return true;
    }

    public boolean unblockMods(List<String> modNames, UUID playerUuid) {
        FeatherPlayer featherPlayer = FeatherAPI.getPlayerService().getPlayer(playerUuid);
        if (featherPlayer == null) {
            return false;
        }

        List<FeatherMod> mods = modNames.stream()
                .map(FeatherMod::new)
                .collect(Collectors.toList());

        featherPlayer.unblockMods(mods);

        return true;
    }
    
    public CompletableFuture<List<String>> getBlockedMods(UUID playerUuid) {
        FeatherPlayer featherPlayer = FeatherAPI.getPlayerService().getPlayer(playerUuid);
        if (featherPlayer == null) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }

        return featherPlayer.getBlockedMods()
                .thenApply(mods -> mods.stream()
                        .map(FeatherMod::getName)
                        .collect(Collectors.toList()));
    }

    public CompletableFuture<List<String>> getEnabledMods(UUID playerUuid) {
        FeatherPlayer featherPlayer = FeatherAPI.getPlayerService().getPlayer(playerUuid);
        if (featherPlayer == null) {
            return CompletableFuture.completedFuture(new ArrayList<>());
        }

        return featherPlayer.getEnabledMods()
                .thenApply(mods -> mods.stream()
                        .map(FeatherMod::getName)
                        .collect(Collectors.toList()));
    }

    public boolean isFeatherPlayer(UUID playerUuid) {
        return FeatherAPI.getPlayerService().getPlayer(playerUuid) != null;
    }

    public FeatherPlayer getFeatherPlayer(UUID playerUuid) {
        return FeatherAPI.getPlayerService().getPlayer(playerUuid);
    }

    public Collection<FeatherPlayer> getAllFeatherPlayers() {
        return FeatherAPI.getPlayerService().getPlayers();
    }
    
    public List<String> getDefaultBlockedMods() {
        return new ArrayList<>(defaultBlockedMods);
    }

    public boolean isValidModName(String modName) {
        // Basic validation - mod names should be lowercase and contain only letters, numbers, and underscores
        return modName != null && modName.matches("^[a-z0-9_]+$");
    }

    public void blockModForAllPlayers(String modName) {
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        FeatherMod mod = new FeatherMod(modName);

        for (FeatherPlayer featherPlayer : featherPlayers) {
            try {
                featherPlayer.blockMods(List.of(mod));
            } catch (Exception e) {
                // Silently ignore errors for individual players
            }
        }
    }

    public void unblockModForAllPlayers(String modName) {
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        FeatherMod mod = new FeatherMod(modName);

        for (FeatherPlayer featherPlayer : featherPlayers) {
            try {
                featherPlayer.unblockMods(List.of(mod));
            } catch (Exception e) {
                // Silently ignore errors for individual players
            }
        }
    }
}
