package me.darkness.featheraddons.managers;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.meta.ServerListBackground;
import net.digitalingot.feather.serverapi.api.meta.ServerListBackgroundFactory;
import org.bukkit.Bukkit;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;

public class ServerBackgroundManager {
    
    private final FeatherAddonsPlugin plugin;
    private ServerListBackground currentBackground;
    
    public ServerBackgroundManager(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;

        // Załaduj tło od razu przy inicjalizacji
        if (plugin.getConfigManager().isServerBackgroundEnabled()) {
            String imageUrl = plugin.getConfigManager().getBackgroundImageUrl();
            if (!imageUrl.isEmpty()) {
                // Załaduj tło asynchronicznie
                Bukkit.getScheduler().runTaskAsynchronously(plugin, this::loadBackground);
            }
        }
    }
    
    public void reload() {
        if (plugin.getConfigManager().isServerBackgroundEnabled()) {
            String imageUrl = plugin.getConfigManager().getBackgroundImageUrl();
            if (!imageUrl.isEmpty()) {
                loadBackground();
            }
        }
    }
    
    public void loadBackground() {
        if (!plugin.getConfigManager().isServerBackgroundEnabled()) {
            return;
        }
        
        String imageUrl = plugin.getConfigManager().getBackgroundImageUrl();
        if (imageUrl == null || imageUrl.isEmpty()) {
            plugin.getLogger().info("No background image URL specified in config");
            return;
        }
        
        plugin.getLogger().info("Loading background from URL: " + imageUrl);
        
        try {
            loadBackgroundFromUrl(imageUrl);
            plugin.getLogger().info("Server background loaded successfully from URL");
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to load server background: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void loadBackgroundFromUrl(String imageUrl) throws Exception {
        try {
            URL url = new URL(imageUrl);
            byte[] imageBytes = downloadImageFromUrl(url);

            ServerListBackgroundFactory factory = FeatherAPI.getMetaService().getServerListBackgroundFactory();
            ServerListBackground background = factory.fromBytes(imageBytes);

            FeatherAPI.getMetaService().setServerListBackground(background);
            this.currentBackground = background;

        } catch (Exception e) {
            throw new RuntimeException("Failed to set server background from URL: " + e.getMessage(), e);
        }
    }

    private byte[] downloadImageFromUrl(URL url) throws Exception {
        try (InputStream inputStream = url.openStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        }
    }
    
    public boolean setBackgroundUrl(String imageUrl) {
        if (!plugin.getConfigManager().isServerBackgroundEnabled()) {
            return false;
        }
        
        if (imageUrl == null || imageUrl.isEmpty()) {
            plugin.getLogger().warning("Background URL cannot be empty");
            return false;
        }
        
        try {
            loadBackgroundFromUrl(imageUrl);
            
            // Update config
            plugin.getConfigManager().setConfigValue("server-background.image-url", imageUrl);
            
            plugin.getLogger().info("Server background changed to URL: " + imageUrl);
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to set server background: " + e.getMessage());
            return false;
        }
    }
    
    public boolean removeBackground() {
        try {
            // Clear the background
            FeatherAPI.getMetaService().setServerListBackground(null);
            this.currentBackground = null;
            
            plugin.getLogger().info("Server background removed");
            return true;
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to remove server background: " + e.getMessage());
            return false;
        }
    }
    
    public ServerListBackground getCurrentBackground() {
        return currentBackground;
    }
    
    public boolean hasBackground() {
        return currentBackground != null;
    }
    
    public String getBackgroundInfo() {
        if (!hasBackground()) {
            return "No background currently set";
        }
        
        String imageUrl = plugin.getConfigManager().getBackgroundImageUrl();
        return String.format("Current background URL: %s", imageUrl);
    }

    public void forceRefreshBackground() {
        // Próba wymuszenia odświeżenia tła przez ponowne ustawienie
        if (!hasBackground()) {
            loadBackground();
            return;
        }

        try {
            // Usuń tło
            FeatherAPI.getMetaService().setServerListBackground(null);

            // Poczekaj chwilę
            Thread.sleep(100);

            // Ustaw ponownie
            FeatherAPI.getMetaService().setServerListBackground(currentBackground);

        } catch (Exception e) {
            // Spróbuj załadować ponownie
            currentBackground = null;
            loadBackground();
        }
    }
}
