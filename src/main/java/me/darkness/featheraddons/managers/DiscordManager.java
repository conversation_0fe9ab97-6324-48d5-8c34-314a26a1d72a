package me.darkness.featheraddons.managers;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import net.digitalingot.feather.serverapi.api.FeatherAPI;
import net.digitalingot.feather.serverapi.api.player.FeatherPlayer;
import net.digitalingot.feather.serverapi.api.meta.DiscordActivity;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitTask;

import java.util.Collection;

public class DiscordManager {

    private final FeatherAddonsPlugin plugin;
    private BukkitTask updateTask;
    private DiscordActivity defaultActivity;

    public DiscordManager(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
        loadConfiguration();
        startUpdateTask();
    }
    
    public void loadConfiguration() {
        if (!plugin.getConfigManager().isDiscordEnabled()) {
            return;
        }

        // Create default activity from config
        createDefaultActivity();
    }

    public void reload() {
        stopUpdateTask();
        loadConfiguration();
        startUpdateTask();
    }

    public void shutdown() {
        stopUpdateTask();
        clearAllActivities();
    }

    private void createDefaultActivity() {
        if (!plugin.getConfigManager().isDiscordEnabled()) {
            return;
        }

        DiscordActivity.Builder builder = DiscordActivity.builder();

        // Set server icon if provided
        String serverIcon = plugin.getConfigManager().getServerIcon();
        if (!serverIcon.isEmpty()) {
            builder.withImage(serverIcon);
        }

        // Set server name as image text (tooltip when hovering over icon)
        String serverName = plugin.getConfigManager().getServerName();
        if (!serverName.isEmpty()) {
            builder.withImageText(serverName);
        }

        // Set details (main text) - use server name if details is empty, otherwise use details
        String details = plugin.getConfigManager().getDiscordDetails();
        if (!details.isEmpty()) {
            builder.withDetails(details);
        } else if (!serverName.isEmpty()) {
            // If no details specified, use server name as main text
            builder.withDetails(serverName);
        }

        // Set state (smaller text)
        String state = plugin.getConfigManager().getDiscordState();
        if (!state.isEmpty()) {
            builder.withState(state);
        }

        // Set party size if enabled
        if (plugin.getConfigManager().shouldShowPlayerCount()) {
            int onlinePlayers = Bukkit.getOnlinePlayers().size();
            int maxPlayers = Bukkit.getMaxPlayers();

            // Ensure party size is at least 1 (FeatherAPI requirement)
            if (onlinePlayers < 1) {
                onlinePlayers = 1;
            }
            if (maxPlayers < 1) {
                maxPlayers = 1;
            }

            builder.withPartySize(onlinePlayers, maxPlayers);
        }

        // Set start timestamp
        builder.withStartTimestamp(System.currentTimeMillis());

        this.defaultActivity = builder.build();
    }
    
    private void startUpdateTask() {
        if (!plugin.getConfigManager().isDiscordEnabled()) {
            return;
        }

        // Update every 30 seconds
        updateTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            updateAllActivities();
        }, 600L, 600L); // 30 seconds in ticks
    }
    
    private void stopUpdateTask() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }
    }
    
    public void updateAllActivities() {
        if (!plugin.getConfigManager().isDiscordEnabled() || defaultActivity == null) {
            return;
        }
        
        // Recreate activity with current player count
        createDefaultActivity();
        
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        
        for (FeatherPlayer player : featherPlayers) {
            try {
                FeatherAPI.getMetaService().updateDiscordActivity(player, defaultActivity);
            } catch (Exception e) {
                // Silently ignore errors
            }
        }
    }
    
    public void updatePlayerActivity(FeatherPlayer player) {
        if (!plugin.getConfigManager().isDiscordEnabled() || defaultActivity == null) {
            return;
        }
        
        try {
            FeatherAPI.getMetaService().updateDiscordActivity(player, defaultActivity);
        } catch (Exception e) {
            // Silently ignore errors
        }
    }
    
    public void clearPlayerActivity(FeatherPlayer player) {
        try {
            FeatherAPI.getMetaService().clearDiscordActivity(player);
        } catch (Exception e) {
            // Silently ignore errors
        }
    }
    
    public void clearAllActivities() {
        Collection<FeatherPlayer> featherPlayers = FeatherAPI.getPlayerService().getPlayers();
        
        for (FeatherPlayer player : featherPlayers) {
            clearPlayerActivity(player);
        }
    }
    
    public DiscordActivity createCustomActivity(String details, String state, String imageUrl, String imageText) {
        DiscordActivity.Builder builder = DiscordActivity.builder();
        
        if (details != null && !details.isEmpty()) {
            builder.withDetails(details);
        }
        
        if (state != null && !state.isEmpty()) {
            builder.withState(state);
        }
        
        if (imageUrl != null && !imageUrl.isEmpty()) {
            builder.withImage(imageUrl);
        }
        
        if (imageText != null && !imageText.isEmpty()) {
            builder.withImageText(imageText);
        }
        
        // Add party size if enabled
        if (plugin.getConfigManager().shouldShowPlayerCount()) {
            int onlinePlayers = Bukkit.getOnlinePlayers().size();
            int maxPlayers = Bukkit.getMaxPlayers();

            // Ensure party size is at least 1 (FeatherAPI requirement)
            if (onlinePlayers < 1) {
                onlinePlayers = 1;
            }
            if (maxPlayers < 1) {
                maxPlayers = 1;
            }

            builder.withPartySize(onlinePlayers, maxPlayers);
        }
        
        builder.withStartTimestamp(System.currentTimeMillis());
        
        return builder.build();
    }

    public boolean isEnabled() {
        return plugin.getConfigManager().isDiscordEnabled();
    }

    public DiscordActivity getDefaultActivity() {
        return defaultActivity;
    }
}
