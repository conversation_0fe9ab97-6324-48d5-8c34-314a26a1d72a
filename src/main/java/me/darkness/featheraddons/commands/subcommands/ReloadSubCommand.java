package me.darkness.featheraddons.commands.subcommands;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import me.darkness.featheraddons.commands.SubCommand;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ReloadSubCommand implements SubCommand {
    
    private final FeatherAddonsPlugin plugin;
    
    public ReloadSubCommand(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "reload";
    }
    
    @Override
    public String getDescription() {
        return "Reload plugin configuration";
    }
    
    @Override
    public String getUsage() {
        return "/777featheraddons reload";
    }
    
    @Override
    public String getPermission() {
        return "featheraddons.reload";
    }
    
    @Override
    public List<String> getAliases() {
        return Arrays.asList("rl", "r");
    }
    
    @Override
    public void execute(CommandSender sender, String[] args) {
        try {
            plugin.reloadConfiguration();
            plugin.getMessageUtils().sendMessage(sender, "config-reloaded");
        } catch (Exception e) {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&cError reloading configuration: " + e.getMessage());
            plugin.getLogger().severe("Error reloading configuration: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getTabCompletions(CommandSender sender, String[] args) {
        return new ArrayList<>();
    }
}
