package me.darkness.featheraddons.commands.subcommands;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import me.darkness.featheraddons.commands.SubCommand;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class ModsSubCommand implements SubCommand {
    
    private final FeatherAddonsPlugin plugin;
    private final List<String> commonMods = Arrays.asList(
            "perspective", "zoom", "timer", "fps", "coordinates", "keystrokes",
            "cps", "ping", "armor_status", "item_physics", "motion_blur",
            "brightness", "saturation", "crosshair", "waypoints", "minimap"
    );
    
    public ModsSubCommand(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "mods";
    }
    
    @Override
    public String getDescription() {
        return "Manage Feather mods for players";
    }
    
    @Override
    public String getUsage() {
        return "/777featheraddons mods <block|unblock|list> <mod>";
    }
    
    @Override
    public String getPermission() {
        return "featheraddons.mods";
    }
    
    @Override
    public List<String> getAliases() {
        return Arrays.asList("mod", "m");
    }
    
    @Override
    public void execute(CommandSender sender, String[] args) {
        if (args.length < 1) {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&cUsage: " + getUsage());
            return;
        }

        String action = args[0].toLowerCase();

        // Handle list command separately
        if ("list".equals(action)) {
            executeListAction(sender);
            return;
        }

        if (args.length < 2) {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&cUsage: " + getUsage());
            return;
        }

        String modName = args[1].toLowerCase();

        // Validate mod name
        if (!plugin.getModManager().isValidModName(modName)) {
            plugin.getMessageUtils().sendMessage(sender, "mod-not-found", "mod", modName);
            return;
        }
        
        // Global mod management - no specific player needed
        
        // Execute action
        switch (action) {
            case "block":
                executeBlockAction(sender, modName);
                break;
            case "unblock":
                executeUnblockAction(sender, modName);
                break;
            default:
                plugin.getMessageUtils().sendPrefixedMessage(sender, "&cInvalid action. Use 'block', 'unblock', or 'list'");
                break;
        }
    }
    
    private void executeBlockAction(CommandSender sender, String modName) {
        // Add mod to blocked list in config
        List<String> blockedMods = new ArrayList<>(plugin.getConfigManager().getBlockedMods());
        if (!blockedMods.contains(modName)) {
            blockedMods.add(modName);
            plugin.getConfigManager().setConfigValue("mods.blocked", blockedMods);
            plugin.getMessageUtils().sendMessage(sender, "mod-blocked", "mod", modName);

            // Reload mod manager to apply changes
            plugin.getModManager().reload();

            // Apply block to all online Feather players in real-time
            plugin.getModManager().blockModForAllPlayers(modName);
        } else {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&cMod &e" + modName + "&c jest już zablokowany");
        }
    }
    
    private void executeUnblockAction(CommandSender sender, String modName) {
        // Remove mod from blocked list in config
        List<String> blockedMods = new ArrayList<>(plugin.getConfigManager().getBlockedMods());
        if (blockedMods.contains(modName)) {
            blockedMods.remove(modName);
            plugin.getConfigManager().setConfigValue("mods.blocked", blockedMods);
            plugin.getMessageUtils().sendMessage(sender, "mod-unblocked", "mod", modName);

            // Reload mod manager to apply changes
            plugin.getModManager().reload();

            // Apply unblock to all online Feather players in real-time
            plugin.getModManager().unblockModForAllPlayers(modName);
        } else {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&cMod &e" + modName + "&c nie jest zablokowany");
        }
    }

    private void executeListAction(CommandSender sender) {
        List<String> blockedMods = plugin.getConfigManager().getBlockedMods();
        if (blockedMods.isEmpty()) {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&8» &aŻadne mody nie są zablokowane");
        } else {
            String modsList = String.join(", ", blockedMods);
            plugin.getMessageUtils().sendMessage(sender, "blocked-mods-list", "mods", modsList);
        }
    }

    @Override
    public List<String> getTabCompletions(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // Complete action
            completions.addAll(Arrays.asList("block", "unblock", "list"));
        } else if (args.length == 2) {
            String action = args[0].toLowerCase();
            String input = args[1].toLowerCase();

            if ("block".equals(action)) {
                // Show available mods that are NOT blocked
                List<String> blockedMods = plugin.getConfigManager().getBlockedMods();
                completions.addAll(commonMods.stream()
                        .filter(mod -> !blockedMods.contains(mod) && mod.startsWith(input))
                        .collect(Collectors.toList()));
            } else if ("unblock".equals(action)) {
                // Show only blocked mods
                List<String> blockedMods = plugin.getConfigManager().getBlockedMods();
                completions.addAll(blockedMods.stream()
                        .filter(mod -> mod.startsWith(input))
                        .collect(Collectors.toList()));
            }
            // For "list" action, no mod name needed
        }

        return completions;
    }
}
