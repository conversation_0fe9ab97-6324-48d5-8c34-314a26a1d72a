package me.darkness.featheraddons.commands;

import me.darkness.featheraddons.FeatherAddonsPlugin;
import me.darkness.featheraddons.commands.subcommands.ModsSubCommand;
import me.darkness.featheraddons.commands.subcommands.ReloadSubCommand;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class FeatherAddonsCommand implements CommandExecutor, TabCompleter {
    
    private final FeatherAddonsPlugin plugin;
    private final List<SubCommand> subCommands;
    
    public FeatherAddonsCommand(FeatherAddonsPlugin plugin) {
        this.plugin = plugin;
        this.subCommands = new ArrayList<>();
        
        // Register subcommands
        registerSubCommands();
    }
    
    private void registerSubCommands() {
        subCommands.add(new ReloadSubCommand(plugin));
        subCommands.add(new ModsSubCommand(plugin));
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Check basic permission
        if (!sender.hasPermission("featheraddons.use")) {
            plugin.getMessageUtils().sendMessage(sender, "no-permission");
            return true;
        }
        
        // If no arguments, show available commands
        if (args.length == 0) {
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&6Available commands:");
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&e/777featheraddons reload &7- Reload configuration");
            plugin.getMessageUtils().sendPrefixedMessage(sender, "&e/777featheraddons mods <block|unblock|list> <mod> &7- Manage mods globally");
            return true;
        }
        
        String subCommandName = args[0].toLowerCase();
        
        // Find and execute subcommand
        for (SubCommand subCommand : subCommands) {
            if (subCommand.getName().equalsIgnoreCase(subCommandName) || 
                subCommand.getAliases().contains(subCommandName)) {
                
                // Check permission
                if (!sender.hasPermission(subCommand.getPermission())) {
                    plugin.getMessageUtils().sendMessage(sender, "no-permission");
                    return true;
                }
                
                // Execute subcommand
                String[] subArgs = Arrays.copyOfRange(args, 1, args.length);
                subCommand.execute(sender, subArgs);
                return true;
            }
        }
        
        // Unknown subcommand
        plugin.getMessageUtils().sendMessage(sender, "invalid-command");
        return true;
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // Complete subcommand names
            for (SubCommand subCommand : subCommands) {
                if (sender.hasPermission(subCommand.getPermission())) {
                    completions.add(subCommand.getName());
                    completions.addAll(subCommand.getAliases());
                }
            }
            
            // Filter by what the player has typed
            return completions.stream()
                    .filter(completion -> completion.toLowerCase().startsWith(args[0].toLowerCase()))
                    .collect(Collectors.toList());
        }
        
        if (args.length > 1) {
            // Find the subcommand and get its tab completions
            String subCommandName = args[0].toLowerCase();
            
            for (SubCommand subCommand : subCommands) {
                if ((subCommand.getName().equalsIgnoreCase(subCommandName) || 
                     subCommand.getAliases().contains(subCommandName)) &&
                    sender.hasPermission(subCommand.getPermission())) {
                    
                    String[] subArgs = Arrays.copyOfRange(args, 1, args.length);
                    return subCommand.getTabCompletions(sender, subArgs);
                }
            }
        }
        
        return completions;
    }
    
    public List<SubCommand> getSubCommands() {
        return new ArrayList<>(subCommands);
    }
}
