package me.darkness.featheraddons.commands;

import org.bukkit.command.CommandSender;

import java.util.List;

public interface SubCommand {
    
    String getName();
    
    String getDescription();
    
    String getUsage();
    
    String getPermission();
    
    List<String> getAliases();
    
    void execute(CommandSender sender, String[] args);
    
    List<String> getTabCompletions(CommandSender sender, String[] args);
}
