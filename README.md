# 777-FeatherAddons

Zaawansowany plugin do zarządzania funkcjami klienta Feather na serwerze Minecraft.

## Funkcje

### 🔧 Zarządzanie modami
- Blokowanie/odblokowanie modów przez konfigurację
- Komendy do zarządzania modami: `/777featheraddons mods <block|unblock> <mod> [player]`
- Automatyczne blokowanie modów dla nowych graczy
- Powiadomienia dla graczy o zablokowanych modach

### 🎮 Discord Rich Presence
- Konfiguracja niestandardowego statusu Discord
- Ustawienie ikony serwera, opisu, stanu
- Wyświetlanie liczby graczy online
- Automatyczne aktualizacje statusu

### 🖼️ Server List Background
- Niestandardowe tło serwera na liście serwerów Feather
- Obsługa plików PNG (max 1009x202px, 512KB)
- Walidacja rozmiaru i formatu obrazów
- Automatyczne ładowanie przy starcie serwera

## Wymagania

- **Minecraft Server**: Bukkit/Spigot/Paper 1.18+
- **Java**: 17+
- **FeatherServerAPI**: Plugin wymagany do działania
  - Pobierz z: https://github.com/FeatherMC/feather-server-api/releases

## Instalacja

1. Pobierz plugin FeatherServerAPI i umieść w folderze `plugins/`
2. Pobierz 777-FeatherAddons i umieść w folderze `plugins/`
3. Uruchom serwer
4. Skonfiguruj plugin w `plugins/777-FeatherAddons/config.yml`
5. Zrestartuj serwer lub użyj `/777featheraddons reload`

## Konfiguracja

### Podstawowa konfiguracja (config.yml)

```yaml
# Zarządzanie modami
mods:
  blocked:
    - "perspective"
    - "zoom" 
    - "timer"
  auto-block-on-join: true
  notify-players: true

# Discord Rich Presence
discord:
  enabled: true
  server-icon: "https://example.com/server-icon.png"
  server-name: "777 Server"
  details: "Playing on 777 Server"
  state: "Survival Mode"
  show-player-count: true

# Server List Background
server-background:
  enabled: true
  image-file: "server-background.png"
  load-on-startup: true
```

### Tło serwera

1. Umieść plik PNG w folderze `plugins/777-FeatherAddons/backgrounds/`
2. Wymagania obrazu:
   - Format: PNG
   - Zalecane wymiary: 909x102 pikseli
   - Maksymalne wymiary: 1009x202 pikseli
   - Maksymalny rozmiar: 512KB
3. Ustaw nazwę pliku w konfiguracji: `server-background.image-file`

## Komendy

### Główna komenda
- `/777featheraddons` - Wyświetla pomoc
- Aliasy: `/777fa`, `/featheraddons`

### Podkomendy
- `/777featheraddons help` - Wyświetla pomoc
- `/777featheraddons reload` - Przeładowuje konfigurację
- `/777featheraddons mods <block|unblock> <mod> [player]` - Zarządza modami

### Przykłady użycia
```
/777featheraddons mods block perspective
/777featheraddons mods unblock zoom player123
/777featheraddons reload
```

## Uprawnienia

- `featheraddons.*` - Wszystkie uprawnienia (domyślnie: op)
- `featheraddons.use` - Podstawowe użycie (domyślnie: true)
- `featheraddons.admin` - Komendy administracyjne (domyślnie: op)
- `featheraddons.mods` - Zarządzanie modami (domyślnie: op)
- `featheraddons.reload` - Przeładowanie konfiguracji (domyślnie: op)

## Obsługiwane mody

Plugin obsługuje wszystkie mody Feather, w tym:
- `perspective` - Freelook
- `zoom` - Przybliżanie
- `timer` - Timer
- `fps` - Wyświetlanie FPS
- `coordinates` - Współrzędne
- `keystrokes` - Wyświetlanie klawiszy
- `cps` - Kliknięcia na sekundę
- `ping` - Ping do serwera
- I wiele innych...

Pełna lista: https://docs.feathermc.com/server-api/mods

## Struktura projektu

```
src/main/java/me/darkness/featheraddons/
├── FeatherAddonsPlugin.java          # Główna klasa pluginu
├── commands/                         # Komendy
│   ├── FeatherAddonsCommand.java
│   ├── SubCommand.java
│   └── subcommands/
│       ├── HelpSubCommand.java
│       ├── ModsSubCommand.java
│       └── ReloadSubCommand.java
├── configuration/                    # Zarządzanie konfiguracją
│   └── ConfigManager.java
├── listeners/                        # Nasłuchiwacze eventów
│   └── FeatherPlayerListener.java
├── managers/                         # Menedżery funkcjonalności
│   ├── DiscordManager.java
│   ├── ModManager.java
│   └── ServerBackgroundManager.java
└── utils/                           # Narzędzia pomocnicze
    └── MessageUtils.java
```

## Kompilacja

```bash
mvn clean package
```

Skompilowany plik JAR znajdziesz w folderze `target/`.

## Wsparcie

Jeśli napotkasz problemy:
1. Sprawdź czy FeatherServerAPI jest zainstalowane
2. Sprawdź logi serwera pod kątem błędów
3. Upewnij się, że konfiguracja jest poprawna
4. Sprawdź uprawnienia graczy

## Licencja

Ten projekt jest udostępniony na licencji MIT.

## Autor

Stworzony przez darkness dla społeczności 777 Server.
